#!/usr/bin/env python3
"""
Test script to verify models can be loaded without errors
"""

import joblib
import numpy as np

def test_model_loading():
    """Test loading all models"""
    models_to_test = [
        'models/diabetes_model.sav',
        'models/heart_disease_model.sav', 
        'models/parkinsons_model.sav',
        'models/liver_model.sav',
        'models/hepititisc_model.sav',
        'models/chronic_model.sav'
    ]
    
    print("Testing model loading...")
    
    for model_path in models_to_test:
        try:
            print(f"Loading {model_path}...")
            model = joblib.load(model_path)
            print(f"✅ {model_path} loaded successfully")
            
            # Test prediction with dummy data
            if 'diabetes' in model_path:
                test_data = np.random.rand(1, 8)
            elif 'heart' in model_path:
                test_data = np.random.rand(1, 13)
            elif 'parkinsons' in model_path:
                test_data = np.random.rand(1, 22)
            elif 'liver' in model_path:
                test_data = np.random.rand(1, 10)
            elif 'hepatitis' in model_path:
                test_data = np.random.rand(1, 19)
            elif 'chronic' in model_path:
                test_data = np.random.rand(1, 24)
            
            prediction = model.predict(test_data)
            print(f"✅ {model_path} prediction test successful: {prediction}")
            
        except Exception as e:
            print(f"❌ Error with {model_path}: {e}")
    
    print("\nModel loading test completed!")

if __name__ == "__main__":
    test_model_loading()
