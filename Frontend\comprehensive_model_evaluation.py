#!/usr/bin/env python3
"""
Comprehensive model evaluation script for all disease prediction models
Evaluates Random Forest, SVM, and XGBoost with accuracy, precision, recall, F1-score, and response time
"""

import pandas as pd
import numpy as np
import time
import json
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, classification_report
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
import xgboost as xgb
import joblib
import warnings
warnings.filterwarnings('ignore')

class ComprehensiveModelEvaluator:
    def __init__(self):
        self.results = {}
        
    def create_synthetic_data(self, disease_type, n_samples=1000):
        """Create synthetic data for different diseases"""
        np.random.seed(42)
        
        if disease_type == 'diabetes':
            # 8 features: pregnancies, glucose, blood_pressure, skin_thickness, insulin, bmi, diabetes_pedigree, age
            X = np.random.rand(n_samples, 8)
            X[:, 1] = X[:, 1] * 200  # glucose (0-200)
            X[:, 2] = X[:, 2] * 100 + 60  # blood pressure (60-160)
            X[:, 5] = X[:, 5] * 20 + 15  # BMI (15-35)
            X[:, 7] = X[:, 7] * 60 + 20  # age (20-80)
            y = ((X[:, 1] > 140) | (X[:, 5] > 30)).astype(int)
            
        elif disease_type == 'heart':
            # 13 features for heart disease
            X = np.random.rand(n_samples, 13)
            X[:, 0] = X[:, 0] * 50 + 30  # age (30-80)
            X[:, 3] = X[:, 3] * 80 + 90  # blood pressure (90-170)
            X[:, 4] = X[:, 4] * 300 + 150  # cholesterol (150-450)
            X[:, 7] = X[:, 7] * 100 + 70  # max heart rate (70-170)
            y = ((X[:, 0] > 55) | (X[:, 4] > 250) | (X[:, 7] < 100)).astype(int)
            
        elif disease_type == 'parkinsons':
            # 22 voice measurements
            X = np.random.rand(n_samples, 22)
            y = ((X[:, 0] > 0.6) | (X[:, 5] > 0.7)).astype(int)
            
        elif disease_type == 'liver':
            # 10 features for liver disease
            X = np.random.rand(n_samples, 10)
            X[:, 0] = X[:, 0] * 60 + 20  # age (20-80)
            X[:, 2] = X[:, 2] * 10  # total bilirubin
            X[:, 4] = X[:, 4] * 500 + 100  # alkaline phosphotase
            y = ((X[:, 2] > 2.0) | (X[:, 4] > 300)).astype(int)
            
        elif disease_type == 'hepatitis':
            # 19 features for hepatitis
            X = np.random.rand(n_samples, 19)
            X[:, 0] = X[:, 0] * 60 + 20  # age
            y = (X[:, 0] > 50).astype(int)
            
        elif disease_type == 'chronic_kidney':
            # 24 features for chronic kidney disease
            X = np.random.rand(n_samples, 24)
            X[:, 0] = X[:, 0] * 60 + 20  # age
            X[:, 1] = X[:, 1] * 100 + 60  # blood pressure
            y = ((X[:, 0] > 55) | (X[:, 1] > 140)).astype(int)
            
        return X, y
    
    def evaluate_model(self, model, X_train, X_test, y_train, y_test, model_name, disease_type):
        """Evaluate a single model and return metrics with response time"""
        
        # Training time
        start_time = time.time()
        model.fit(X_train, y_train)
        training_time = time.time() - start_time
        
        # Prediction time
        start_time = time.time()
        y_pred = model.predict(X_test)
        prediction_time = time.time() - start_time
        
        # Calculate metrics
        accuracy = accuracy_score(y_test, y_pred)
        precision = precision_score(y_test, y_pred, average='weighted', zero_division=0)
        recall = recall_score(y_test, y_pred, average='weighted', zero_division=0)
        f1 = f1_score(y_test, y_pred, average='weighted', zero_division=0)
        
        # Response time per prediction (in milliseconds)
        response_time_ms = (prediction_time / len(X_test)) * 1000
        
        return {
            'model': model_name,
            'disease': disease_type,
            'accuracy': round(accuracy, 4),
            'precision': round(precision, 4),
            'recall': round(recall, 4),
            'f1_score': round(f1, 4),
            'training_time_seconds': round(training_time, 4),
            'prediction_time_seconds': round(prediction_time, 4),
            'response_time_per_prediction_ms': round(response_time_ms, 4),
            'test_samples': len(X_test)
        }
    
    def evaluate_disease(self, disease_type):
        """Evaluate all three models for a specific disease"""
        print(f"\n🔍 Evaluating {disease_type.upper()} prediction models...")
        
        # Create synthetic data
        X, y = self.create_synthetic_data(disease_type)
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # Define models
        models = {
            'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
            'SVM': SVC(kernel='rbf', random_state=42),
            'XGBoost': xgb.XGBClassifier(random_state=42, eval_metric='logloss')
        }
        
        disease_results = []
        
        for model_name, model in models.items():
            try:
                result = self.evaluate_model(model, X_train, X_test, y_train, y_test, model_name, disease_type)
                disease_results.append(result)
                print(f"✅ {model_name}: Accuracy={result['accuracy']}, Response Time={result['response_time_per_prediction_ms']:.2f}ms")
            except Exception as e:
                print(f"❌ Error evaluating {model_name} for {disease_type}: {e}")
        
        return disease_results
    
    def run_comprehensive_evaluation(self):
        """Run evaluation for all diseases and all models"""
        print("🚀 Starting Comprehensive Model Evaluation")
        print("=" * 80)
        
        diseases = ['diabetes', 'heart', 'parkinsons', 'liver', 'hepatitis', 'chronic_kidney']
        all_results = []
        
        for disease in diseases:
            disease_results = self.evaluate_disease(disease)
            all_results.extend(disease_results)
        
        # Save results
        self.results = all_results
        
        # Save to JSON file
        with open('model_evaluation_results.json', 'w') as f:
            json.dump(all_results, f, indent=2)
        
        return all_results
    
    def print_summary_table(self):
        """Print a formatted summary table"""
        print("\n" + "=" * 120)
        print("📊 COMPREHENSIVE MODEL EVALUATION RESULTS")
        print("=" * 120)
        
        # Group by disease
        diseases = {}
        for result in self.results:
            disease = result['disease']
            if disease not in diseases:
                diseases[disease] = []
            diseases[disease].append(result)
        
        for disease, results in diseases.items():
            print(f"\n🏥 {disease.upper()} DISEASE PREDICTION")
            print("-" * 80)
            print(f"{'Model':<15} {'Accuracy':<10} {'Precision':<10} {'Recall':<10} {'F1-Score':<10} {'Response Time (ms)':<20}")
            print("-" * 80)
            
            for result in results:
                print(f"{result['model']:<15} {result['accuracy']:<10} {result['precision']:<10} {result['recall']:<10} {result['f1_score']:<10} {result['response_time_per_prediction_ms']:<20}")
        
        # Overall best performers
        print("\n" + "=" * 80)
        print("🏆 BEST PERFORMING MODELS BY METRIC")
        print("=" * 80)
        
        metrics = ['accuracy', 'precision', 'recall', 'f1_score']
        for metric in metrics:
            best = max(self.results, key=lambda x: x[metric])
            print(f"Best {metric.upper():<10}: {best['model']} ({best['disease']}) - {best[metric]}")
        
        # Fastest response times
        fastest = min(self.results, key=lambda x: x['response_time_per_prediction_ms'])
        print(f"Fastest Response: {fastest['model']} ({fastest['disease']}) - {fastest['response_time_per_prediction_ms']:.2f}ms")
    
    def create_comparison_dataframe(self):
        """Create a pandas DataFrame for easy analysis"""
        df = pd.DataFrame(self.results)
        return df

def main():
    """Main function to run the comprehensive evaluation"""
    evaluator = ComprehensiveModelEvaluator()
    
    # Run evaluation
    results = evaluator.run_comprehensive_evaluation()
    
    # Print summary
    evaluator.print_summary_table()
    
    # Create DataFrame for further analysis
    df = evaluator.create_comparison_dataframe()
    
    # Save DataFrame to CSV
    df.to_csv('model_evaluation_results.csv', index=False)
    
    print(f"\n💾 Results saved to:")
    print("   - model_evaluation_results.json")
    print("   - model_evaluation_results.csv")
    
    print(f"\n🎯 Evaluation completed for {len(results)} model-disease combinations!")
    
    return df

if __name__ == "__main__":
    results_df = main()
